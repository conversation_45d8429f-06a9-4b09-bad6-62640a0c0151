"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAlbumComments, useCreateComment } from "@/lib/hooks/use-comments";
import { CommentWithReplies } from "@/lib/models";
import { cn } from "@/lib/utils";
import {
   ChatBubbleBottomCenterIcon,
   PaperAirplaneIcon,
} from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { MessageCircle, Reply } from "lucide-react";
import { useState } from "react";

// Helper function to generate user initials
function getUserInitials(name: string): string {
   return name
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join("");
}

// Avatar component for consistent styling
interface AvatarProps {
   name: string;
   size?: "sm" | "md" | "lg";
   variant?: "primary" | "secondary";
}

function Avatar({ name, size = "md", variant = "primary" }: AvatarProps) {
   const initials = getUserInitials(name);

   const sizeClasses = {
      sm: "h-6 w-6 text-xs",
      md: "h-8 w-8 text-sm",
      lg: "h-10 w-10 text-base",
   };

   const variantClasses = {
      primary: "bg-primary/10 text-primary border-primary/20",
      secondary: "bg-muted text-muted-foreground border-border/30",
   };

   return (
      <div
         className={cn(
            "flex items-center justify-center rounded-full font-medium border transition-colors",
            sizeClasses[size],
            variantClasses[variant]
         )}
      >
         {initials}
      </div>
   );
}

interface CommentDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   albumId: string;
   albumName: string;
}

interface CommentFormData {
   content: string;
   authorName: string;
   authorEmail: string;
}

interface CommentItemProps {
   comment: CommentWithReplies;
   albumId: string;
   onReply: (parentId: string, authorName: string) => void;
}

function CommentItem({ comment, albumId, onReply }: CommentItemProps) {
   const [showReplyForm, setShowReplyForm] = useState(false);
   const [replyContent, setReplyContent] = useState("");
   const [replyAuthorName, setReplyAuthorName] = useState("");
   const [replyAuthorEmail, setReplyAuthorEmail] = useState("");
   const [isSubmittingReply, setIsSubmittingReply] = useState(false);

   const createComment = useCreateComment();

   const handleReplySubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!replyContent.trim() || !replyAuthorName.trim()) return;

      setIsSubmittingReply(true);
      try {
         await createComment.mutateAsync({
            albumId,
            parentId: comment._id?.toString(),
            content: replyContent.trim(),
            authorName: replyAuthorName.trim(),
            authorEmail: replyAuthorEmail.trim() || undefined,
         });

         setReplyContent("");
         setReplyAuthorName("");
         setReplyAuthorEmail("");
         setShowReplyForm(false);
      } catch (error) {
         console.error("Failed to submit reply:", error);
      } finally {
         setIsSubmittingReply(false);
      }
   };

   return (
      <div className="space-y-3">
         <article
            className="flex items-start gap-3 p-4 rounded-lg bg-card border border-border/80 hover:bg-card/70 transition-colors"
            role="article"
            aria-labelledby={`comment-author-${comment._id}`}
         >
            <Avatar name={comment.authorName} size="md" />
            <div className="flex-1 space-y-2">
               <header className="flex items-center gap-2">
                  <span
                     id={`comment-author-${comment._id}`}
                     className="font-medium text-foreground"
                  >
                     {comment.authorName}
                  </span>
                  <time
                     className="text-xs text-muted-foreground"
                     dateTime={new Date(comment.createdAt).toISOString()}
                  >
                     {new Date(comment.createdAt).toLocaleDateString()}
                  </time>
               </header>
               <p className="text-sm text-foreground/90 leading-relaxed">
                  {comment.content}
               </p>
               <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                     onReply(comment._id?.toString() || "", comment.authorName)
                  }
                  aria-label={`Reply to ${comment.authorName}'s comment`}
                  className="h-auto !p-0 text-xs text-muted-foreground hover:bg-transparent hover:text-foreground"
               >
                  <Reply className="h-3 w-3 mr-0.5" aria-hidden="true" />
                  Reply
               </Button>
            </div>
         </article>

         {/* Replies */}
         {comment.replies.length > 0 && (
            <div className="ml-11 space-y-3 rounded-l-md">
               {comment.replies.map((reply) => (
                  <div
                     key={reply._id?.toString()}
                     className="flex items-start gap-3 p-3 rounded-lg border-border/80 bg-card/80 border"
                  >
                     <Avatar name={reply.authorName} size="sm" />
                     <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                           <span className="text-sm font-medium text-foreground">
                              {reply.authorName}
                           </span>
                           <span className="text-xs text-muted-foreground">
                              {new Date(reply.createdAt).toLocaleDateString()}
                           </span>
                        </div>
                        <p className="text-sm text-foreground/90 leading-relaxed">
                           {reply.content}
                        </p>
                     </div>
                  </div>
               ))}
            </div>
         )}

         {/* Reply Form */}
         {showReplyForm && (
            <form onSubmit={handleReplySubmit} className="ml-11 space-y-3">
               <div className="space-y-2">
                  <Input
                     placeholder="Your name"
                     value={replyAuthorName}
                     onChange={(e) => setReplyAuthorName(e.target.value)}
                     required
                     className="text-sm"
                  />
                  <Input
                     type="email"
                     placeholder="Your email (optional)"
                     value={replyAuthorEmail}
                     onChange={(e) => setReplyAuthorEmail(e.target.value)}
                     className="text-sm"
                  />
                  <Textarea
                     placeholder={`Reply to ${comment.authorName}...`}
                     value={replyContent}
                     onChange={(e) => setReplyContent(e.target.value)}
                     required
                     className="min-h-20 text-sm"
                  />
               </div>
               <div className="flex gap-2">
                  <Button
                     type="submit"
                     size="sm"
                     disabled={
                        isSubmittingReply ||
                        !replyContent.trim() ||
                        !replyAuthorName.trim()
                     }
                     className="flex items-center gap-1"
                  >
                     <PaperAirplaneIcon className="h-3 w-3" />
                     {isSubmittingReply ? "Sending..." : "Send Reply"}
                  </Button>
                  <Button
                     type="button"
                     variant="ghost"
                     size="sm"
                     onClick={() => setShowReplyForm(false)}
                  >
                     Cancel
                  </Button>
               </div>
            </form>
         )}
      </div>
   );
}

export function CommentDialog({
   open,
   onOpenChange,
   albumId,
   albumName,
}: CommentDialogProps) {
   const [formData, setFormData] = useState<CommentFormData>({
      content: "",
      authorName: "",
      authorEmail: "",
   });
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [replyingTo, setReplyingTo] = useState<{
      parentId: string;
      authorName: string;
   } | null>(null);

   const isMobile = useIsMobile();
   const { data: commentsResponse, isLoading } = useAlbumComments(albumId);
   const createComment = useCreateComment();

   const comments = commentsResponse?.data as CommentWithReplies[] | undefined;

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!formData.content.trim() || !formData.authorName.trim()) return;

      setIsSubmitting(true);
      try {
         await createComment.mutateAsync({
            albumId,
            parentId: replyingTo?.parentId,
            content: formData.content.trim(),
            authorName: formData.authorName.trim(),
            authorEmail: formData.authorEmail.trim() || undefined,
         });

         setFormData({ content: "", authorName: "", authorEmail: "" });
         setReplyingTo(null);
      } catch (error) {
         console.error("Failed to submit comment:", error);
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleReply = (parentId: string, authorName: string) => {
      setReplyingTo({ parentId, authorName });
      setFormData((prev) => ({ ...prev, content: "" }));
   };

   const handleCancelReply = () => {
      setReplyingTo(null);
      setFormData((prev) => ({ ...prev, content: "" }));
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent
            className={cn(
               "overflow-hidden flex flex-col",
               isMobile
                  ? "w-full h-[100dvh] max-w-none max-h-none rounded-none border-0 p-0"
                  : "max-w-2xl max-h-[80vh]"
            )}
            onWheel={(e) => {
               // Prevent event bubbling to parent elements when scrolling within dialog
               e.stopPropagation();
            }}
         >
            <DialogHeader
               className={cn("shrink-0 text-left", isMobile ? "p-4 pb-2" : "")}
            >
               <DialogTitle className="flex items-center gap-2">
                  <ChatBubbleBottomCenterIcon
                     className="h-5 w-5"
                     aria-hidden="true"
                  />
                  Comments for {albumName}
               </DialogTitle>
               <DialogDescription>
                  Share your thoughts about this album
               </DialogDescription>
            </DialogHeader>

            <div
               className={cn(
                  "flex-1 overflow-hidden flex flex-col",
                  isMobile ? "px-4" : ""
               )}
            >
               {/* Comments List Section */}
               <div
                  className={cn(
                     "flex-1 overflow-y-auto bg-[hsl(0_0%_8%_/_70%)] border border-border/20 border-b-0",
                     isMobile ? "rounded-none" : "rounded-lg"
                  )}
                  role="region"
                  aria-label="Comments list"
               >
                  <div className={cn("space-y-4", isMobile ? "p-3" : "p-4")}>
                     {isLoading ? (
                        <div className="flex items-center justify-center py-8">
                           <div className="text-muted-foreground">
                              Loading comments...
                           </div>
                        </div>
                     ) : comments && comments.length > 0 ? (
                        comments.map((comment, index) => (
                           <motion.div
                              key={comment._id?.toString()}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                           >
                              <CommentItem
                                 comment={comment}
                                 albumId={albumId}
                                 onReply={handleReply}
                              />
                           </motion.div>
                        ))
                     ) : (
                        <div className="flex items-center justify-center py-12">
                           <div className="text-center text-muted-foreground">
                              <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-40" />
                              <p className="text-lg font-medium mb-1">
                                 No comments yet
                              </p>
                              <p className="text-sm">
                                 Be the first to share your thoughts!
                              </p>
                           </div>
                        </div>
                     )}
                  </div>
               </div>

               {/* Comment Form Section */}
               <div
                  className={cn(
                     "bg-card border border-border/30 backdrop-blur-sm mt-4",
                     isMobile ? "rounded-none p-3" : "rounded-lg p-4"
                  )}
               >
                  {replyingTo && (
                     <div className="flex gap-2 mb-3 p-2 bg-muted/5 rounded-md text-sm">
                        <span className="text-muted-foreground">
                           Replying to{" "}
                        </span>
                        <span className="font-medium">
                           {replyingTo.authorName}
                        </span>
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={handleCancelReply}
                           className="ml-auto h-auto p-0 hover:bg-transparent text-xs"
                        >
                           Cancel
                        </Button>
                     </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-3">
                     <div
                        className={cn(
                           "grid gap-3",
                           isMobile
                              ? "grid-cols-1"
                              : "grid-cols-1 md:grid-cols-2"
                        )}
                     >
                        <Input
                           placeholder="Your name"
                           value={formData.authorName}
                           onChange={(e) =>
                              setFormData((prev) => ({
                                 ...prev,
                                 authorName: e.target.value,
                              }))
                           }
                           required
                           aria-label="Your name"
                           className={cn(isMobile ? "h-12 text-base" : "")}
                        />
                        <Input
                           type="email"
                           placeholder="Your email (optional)"
                           value={formData.authorEmail}
                           onChange={(e) =>
                              setFormData((prev) => ({
                                 ...prev,
                                 authorEmail: e.target.value,
                              }))
                           }
                           aria-label="Your email (optional)"
                           className={cn(isMobile ? "h-12 text-base" : "")}
                        />
                     </div>
                     <Textarea
                        placeholder={
                           replyingTo
                              ? `Reply to ${replyingTo.authorName}...`
                              : "Share your thoughts about this album..."
                        }
                        value={formData.content}
                        onChange={(e) =>
                           setFormData((prev) => ({
                              ...prev,
                              content: e.target.value,
                           }))
                        }
                        required
                        aria-label={
                           replyingTo
                              ? `Reply to ${replyingTo.authorName}`
                              : "Share your thoughts about this album"
                        }
                        className={cn(
                           isMobile ? "min-h-32 text-base" : "min-h-24"
                        )}
                     />
                     <div className="flex justify-end">
                        <Button
                           type="submit"
                           disabled={
                              isSubmitting ||
                              !formData.content.trim() ||
                              !formData.authorName.trim()
                           }
                           aria-label={
                              isSubmitting
                                 ? "Sending comment..."
                                 : replyingTo
                                 ? "Send reply"
                                 : "Send comment"
                           }
                           className={cn(
                              "flex items-center gap-2",
                              isMobile ? "h-12 px-6 text-base" : ""
                           )}
                        >
                           <PaperAirplaneIcon
                              className="h-4 w-4"
                              aria-hidden="true"
                           />
                           {isSubmitting
                              ? "Sending..."
                              : replyingTo
                              ? "Send Reply"
                              : "Send Comment"}
                        </Button>
                     </div>
                  </form>
               </div>
            </div>
         </DialogContent>
      </Dialog>
   );
}
